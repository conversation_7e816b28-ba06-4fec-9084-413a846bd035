{"addSearchProvider": {"message": "Add search provider"}, "contextMenuOpenAll": {"message": "Open All"}, "contextMenuTitle": {"message": "Reverse Image Search"}, "errorWhileSaving": {"message": "There was an unknown error while saving."}, "extensionDescription": {"message": "Adds an option to the context menu to reverse search an image on various services"}, "extensionName": {"message": "Reverse Image Search"}, "msgAtLeastOneSearchProvider": {"message": "Please choose at least one search provider!"}, "msgDuplicatedProviderName": {"message": "Some search provider name is duplicated!"}, "msgIconUploadNotImage": {"message": "Please upload available image format! (png, jpg, ico)"}, "msgIconUploadNotSquareImage": {"message": "Please upload square image!"}, "msgIconUploadNotSupported": {"message": "Using icons in context menus is not supported by this browser."}, "msgSuccessSaveOptions": {"message": "Saved!"}, "openInBackgroundLabel": {"message": "Open in background"}, "openTabAtEnd": {"message": "At the end"}, "openTabAtLabel": {"message": "Open the search page"}, "openTabAtLeft": {"message": "Left to the current tab"}, "openTabAtRight": {"message": "Right to the current tab"}, "optionsPageTitle": {"message": "Options"}, "providerNamePlaceholder": {"message": "2-15 letters"}, "providerNamePlaceholderError": {"message": "The name of provider $index$ is invalid. Please use 2-15 letters.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s):// at the beginning and %s for the image url..."}, "providerURLPlaceholderPOST": {"message": "http(s):// at the beginning..."}, "providerURLPlaceholderError": {"message": "The url of provider $index$ is invalid. Use http(s):// at the beginning and %s as a placeholder for the image url.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderErrorPOST": {"message": "The url of provider $index$ is invalid. Use http(s):// at the beginning.", "placeholders": {"index": {"content": "$1"}}}, "doNotEncodeUrlLabel": {"message": "Do not encode URL"}, "stripProtocolLabel": {"message": "Remove protocol from image URL"}, "httpMethodLabel": {"message": "HTTP Method"}, "postFieldNameLabel": {"message": "POST field name"}, "contentTypeLabel": {"message": "Content Type"}, "restoreDefaultSearchProviders": {"message": "Restore default"}, "saveOptions": {"message": "Save Options"}, "searchAllByDefault": {"message": "Search all providers by default"}, "searchAllByDefault_info": {"message": "When this option is enabled, only one context menu entry is shown and clicking it will open all selected search providers. If this is disabled, a submenu will be created."}, "showOpenAll": {"message": "Show 'Open All'"}, "showOpenAllAtTop": {"message": "Show 'Open All' at the top"}, "redirectingToSearchEngine": {"message": "Redirecting to search engine..."}, "privacyPolicy": {"message": "Privacy Policy"}, "version": {"message": "Version $version$", "placeholders": {"version": {"content": "$1"}}}}